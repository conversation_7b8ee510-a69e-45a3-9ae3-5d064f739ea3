#!/usr/bin/env python3

import sys
import os
sys.path.append('src')

from map_handle import MapFileParser

def analyze_map_difference():
    """分析map文件两种统计方法的差异"""
    
    map_file = 'int_rpu_arm8r5_arm_gcc.elf.map'
    
    print(f"分析map文件: {map_file}")
    print("=" * 80)
    
    # 创建解析器并分析文件
    parser = MapFileParser(map_file)
    parser.parse_map_file()
    
    # 获取两种统计结果
    text1, data1, bss1, other1 = parser.get_memory_usage()
    text2, data2, bss2, other2 = parser.get_module_memory_usage()
    
    print("=== 内存占用信息 (基于主段统计) ===")
    total1 = text1 + data1 + bss1 + other1
    print(f"text:  {text1:>10,} bytes ({text1/total1*100:5.1f}%)")
    print(f"data:  {data1:>10,} bytes ({data1/total1*100:5.1f}%)")
    print(f"bss:   {bss1:>10,} bytes ({bss1/total1*100:5.1f}%)")
    print(f"other: {other1:>10,} bytes ({other1/total1*100:5.1f}%)")
    print(f"总计:  {total1:>10,} bytes")
    
    print()
    print("=== 模块占用分析 (基于子段统计) ===")
    total2 = text2 + data2 + bss2 + other2
    print(f"text:  {text2:>10,} bytes ({text2/total2*100:5.1f}%)")
    print(f"data:  {data2:>10,} bytes ({data2/total2*100:5.1f}%)")
    print(f"bss:   {bss2:>10,} bytes ({bss2/total2*100:5.1f}%)")
    print(f"other: {other2:>10,} bytes ({other2/total2*100:5.1f}%)")
    print(f"总计:  {total2:>10,} bytes")
    
    print()
    print("=== 差异分析 ===")
    print(f"text差异:  {text2-text1:>10,} bytes ({(text2-text1)/text1*100:+5.1f}%)")
    print(f"data差异:  {data2-data1:>10,} bytes ({(data2-data1)/data1*100:+5.1f}%)")
    print(f"bss差异:   {bss2-bss1:>10,} bytes ({(bss2-bss1)/bss1*100:+5.1f}%)")
    print(f"other差异: {other2-other1:>10,} bytes ({(other2-other1)/max(other1,1)*100:+5.1f}%)")
    print(f"总计差异:  {total2-total1:>10,} bytes ({(total2-total1)/total1*100:+5.1f}%)")
    
    print()
    print("=== 主要段分析 (大于100KB的段) ===")
    print(f"{'段名':<25} {'大小':<12} {'分类':<8} {'地址'}")
    print("-" * 60)
    
    large_sections = []
    for name, info in parser.sections.items():
        if info['size'] > 100000:  # 大于100KB的段
            large_sections.append((name, info))
    
    # 按大小排序
    large_sections.sort(key=lambda x: x[1]['size'], reverse=True)
    
    for name, info in large_sections:
        print(f"{name:<25} {info['size']:>10,} {info['category']:<8} 0x{info['address']:08x}")
    
    print()
    print("=== 分析结论 ===")
    
    if abs(total1 - total2) < 1000:
        print("✓ 两种统计方法的总计基本一致，差异很小")
    else:
        print("✗ 两种统计方法存在显著差异")
        
        if total2 > total1:
            print(f"  模块占用分析比内存占用信息多统计了 {total2-total1:,} bytes")
            print("  可能原因：模块分析包含了一些不应该统计的段，或存在重复计算")
        else:
            print(f"  模块占用分析比内存占用信息少统计了 {total1-total2:,} bytes")
            print("  可能原因：模块分析遗漏了一些没有明确模块归属的段")
    
    # 检查是否有很多other类型的段
    if other1 > total1 * 0.1:  # other超过10%
        print(f"  注意：内存占用信息中other类型占比较高 ({other1/total1*100:.1f}%)")
        print("  建议检查段分类逻辑，将更多段归入text/data/bss类别")
    
    if other2 > total2 * 0.1:  # other超过10%
        print(f"  注意：模块占用分析中other类型占比较高 ({other2/total2*100:.1f}%)")
        print("  建议检查段分类逻辑，将更多段归入text/data/bss类别")

if __name__ == "__main__":
    analyze_map_difference()
