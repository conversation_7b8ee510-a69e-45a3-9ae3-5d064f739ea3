#!/usr/bin/env python3
"""
测试MAP文件分析的段分类优化效果
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from map_handle import MapFileParser

def test_section_classification():
    """测试段分类功能"""
    print("测试段分类功能...")
    
    # 创建一个测试解析器实例
    parser = MapFileParser("dummy.map")  # 不需要真实文件，只测试分类器
    
    # 测试各种段名的分类
    test_cases = [
        # 应该被分类为text的段
        ('.text', 'text'),
        ('.text.main', 'text'),
        ('.text.startup', 'text'),
        ('.init', 'text'),
        ('.fini', 'text'),
        ('.startup_text', 'text'),
        ('.text.libJ6_MCU_MCAL_func', 'text'),
        ('.text.uss_sensl_process', 'text'),
        ('.code_section', 'text'),
        ('.interrupt_handler', 'text'),
        ('.isr_vector', 'text'),
        ('.kernel_code', 'text'),
        ('.os_task', 'text'),
        ('.func_main', 'text'),
        ('.boot_code', 'text'),
        
        # 应该被分类为data的段
        ('.data', 'data'),
        ('.rodata', 'data'),
        ('.mcal_text', 'data'),  # 特殊情况
        ('.const_data', 'data'),
        ('.init_array', 'data'),
        ('.u_boot_list', 'data'),
        ('.config_table', 'data'),
        
        # 应该被分类为bss的段
        ('.bss', 'bss'),
        ('.bss.buffer', 'bss'),
        ('.stack', 'bss'),
        ('.heap', 'bss'),
        ('.noinit', 'bss'),
        ('.zero_init', 'bss'),
        
        # 应该被跳过的段
        ('.debug_info', 'skip'),
        ('.comment', 'skip'),
        ('.note.gnu', 'skip'),
        ('.strtab', 'skip'),
    ]
    
    print("\n段分类测试结果:")
    print("-" * 60)
    print(f"{'段名':<30} {'期望':<10} {'实际':<10} {'结果'}")
    print("-" * 60)
    
    passed = 0
    failed = 0
    
    for section_name, expected in test_cases:
        actual = parser._classify_section_type(section_name)
        result = "✓" if actual == expected else "✗"
        
        if actual == expected:
            passed += 1
        else:
            failed += 1
            
        print(f"{section_name:<30} {expected:<10} {actual:<10} {result}")
    
    print("-" * 60)
    print(f"测试结果: {passed} 通过, {failed} 失败")
    
    if failed > 0:
        print(f"\n⚠️  有 {failed} 个测试失败，需要进一步优化分类逻辑")
    else:
        print("\n✅ 所有测试通过！段分类逻辑工作正常")
    
    return failed == 0

def test_problematic_sections():
    """测试用户提到的问题段"""
    print("\n\n测试用户提到的问题段...")
    
    parser = MapFileParser("dummy.map")
    
    # 模拟一些可能导致问题的段名
    problematic_sections = [
        '.text.libJ6_MCU_MCAL_init',
        '.text.libJ6_MCU_MCAL_main', 
        '.text.libuss_sensl_process',
        '.text.libuss_sensl_handler',
        '.code.libJ6_MCU_MCAL',
        '.startup.libJ6_MCU_MCAL',
        '.func.libuss_sensl',
        '.handler.interrupt',
        '.isr.timer',
        '.kernel.task',
        '.os.scheduler',
        '.boot.init',
        '.exec.main',
        '.proc.handler',
        '.thread.worker',
        '.run.loop'
    ]
    
    print("\n可能的代码段分类测试:")
    print("-" * 50)
    print(f"{'段名':<35} {'分类结果'}")
    print("-" * 50)
    
    text_count = 0
    for section in problematic_sections:
        classification = parser._classify_section_type(section)
        print(f"{section:<35} {classification}")
        if classification == 'text':
            text_count += 1
    
    print("-" * 50)
    print(f"被正确分类为text的段: {text_count}/{len(problematic_sections)}")
    
    if text_count == len(problematic_sections):
        print("✅ 所有可能的代码段都被正确分类为text")
    else:
        print(f"⚠️  有 {len(problematic_sections) - text_count} 个段可能被误分类")

def main():
    """主测试函数"""
    print("MAP文件段分类优化测试")
    print("=" * 60)
    
    # 测试基本分类功能
    basic_test_passed = test_section_classification()
    
    # 测试问题段
    test_problematic_sections()
    
    print("\n" + "=" * 60)
    if basic_test_passed:
        print("✅ 优化完成！段分类逻辑已改进，应该能解决text段被误分类的问题。")
        print("\n主要改进:")
        print("1. 扩展了text段的识别模式")
        print("2. 优化了对包含'text'的段的处理")
        print("3. 添加了更多代码段关键词识别")
        print("4. 改进了智能默认分类逻辑")
        print("5. 统一了段分类器的使用")
    else:
        print("❌ 测试失败，需要进一步调整分类逻辑")

if __name__ == '__main__':
    main()
