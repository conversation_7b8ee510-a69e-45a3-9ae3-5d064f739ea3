#!/usr/bin/env python3

import re

def analyze_map_file_manually():
    """手动分析map文件，找出差异原因"""
    
    map_file = 'int_rpu_arm8r5_arm_gcc.elf.map'
    
    print("手动分析map文件差异")
    print("=" * 60)
    
    # 读取map文件
    with open(map_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # 查找主要段
    print("=== 主要段分析 ===")
    
    # 查找.text段
    text_match = re.search(r'^\.text\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)', content, re.MULTILINE)
    if text_match:
        text_size = int(text_match.group(2), 16)
        print(f".text段:    {text_size:>10,} bytes (0x{text_match.group(2)})")
    
    # 查找.rodata段
    rodata_match = re.search(r'^\.rodata\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)', content, re.MULTILINE)
    if rodata_match:
        rodata_size = int(rodata_match.group(2), 16)
        print(f".rodata段:  {rodata_size:>10,} bytes (0x{rodata_match.group(2)})")
    
    # 查找.data段
    data_match = re.search(r'^\.data\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)', content, re.MULTILINE)
    if data_match:
        data_size = int(data_match.group(2), 16)
        print(f".data段:    {data_size:>10,} bytes (0x{data_match.group(2)})")
    
    # 查找.bss段
    bss_match = re.search(r'^\.bss\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)', content, re.MULTILINE)
    if bss_match:
        bss_size = int(bss_match.group(2), 16)
        print(f".bss段:     {bss_size:>10,} bytes (0x{bss_match.group(2)})")
    
    print()
    print("=== 按ELF标准分类 ===")
    if text_match and rodata_match and data_match and bss_match:
        elf_text = text_size
        elf_data = rodata_size + data_size  # rodata通常归类为data
        elf_bss = bss_size
        elf_total = elf_text + elf_data + elf_bss
        
        print(f"text (可执行):     {elf_text:>10,} bytes ({elf_text/elf_total*100:5.1f}%)")
        print(f"data (已初始化):   {elf_data:>10,} bytes ({elf_data/elf_total*100:5.1f}%)")
        print(f"bss (未初始化):    {elf_bss:>10,} bytes ({elf_bss/elf_total*100:5.1f}%)")
        print(f"总计:              {elf_total:>10,} bytes")
    
    print()
    print("=== 查找其他重要段 ===")
    
    # 查找所有段
    linker_script_match = re.search(r'Linker script and memory map', content)
    if linker_script_match:
        linker_content = content[linker_script_match.end():]
        
        # 查找所有主段
        section_pattern = r'^(\.[a-zA-Z_][^\s]*)\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)'
        matches = re.finditer(section_pattern, linker_content, re.MULTILINE)
        
        sections = []
        for match in matches:
            section_name = match.group(1)
            address = int(match.group(2), 16)
            size = int(match.group(3), 16)
            sections.append((section_name, address, size))
        
        # 按大小排序，显示前20个最大的段
        sections.sort(key=lambda x: x[2], reverse=True)
        
        print("前20个最大的段:")
        print(f"{'段名':<25} {'地址':<12} {'大小':<12}")
        print("-" * 50)
        
        total_all_sections = 0
        for i, (name, addr, size) in enumerate(sections[:20]):
            if size > 0:
                print(f"{name:<25} 0x{addr:08x}   {size:>10,}")
                total_all_sections += size
        
        print(f"\n前20个段总计: {total_all_sections:>10,} bytes")
    
    print()
    print("=== 查找stack段 ===")
    
    # 查找所有stack相关段
    stack_pattern = r'^(\.[A-Z_]*STACK[A-Z_0-9]*)\s+0x([0-9a-f]+)\s+0x([0-9a-f]+)'
    stack_matches = re.finditer(stack_pattern, content, re.MULTILINE)
    
    total_stack_size = 0
    stack_count = 0
    
    for match in stack_matches:
        stack_name = match.group(1)
        stack_size = int(match.group(3), 16)
        total_stack_size += stack_size
        stack_count += 1
        print(f"{stack_name:<20} {stack_size:>8,} bytes")
    
    if stack_count > 0:
        print(f"\n总共 {stack_count} 个stack段，总大小: {total_stack_size:>10,} bytes")
    
    print()
    print("=== 用户报告的数据对比 ===")
    print("内存占用信息:")
    print("  text: 2,181,408 bytes (49.1%)")
    print("  data: 1,086,819 bytes (24.5%)")
    print("  bss:  1,175,104 bytes (26.4%)")
    print("  总计: 4,443,331 bytes")
    print()
    print("模块占用分析:")
    print("  Text:  36,060 bytes")
    print("  Data:  1,124,873 bytes")
    print("  BSS:   1,665,322 bytes")
    print("  Other: 2,097,360 bytes")
    print("  Total: 4,923,615 bytes")

if __name__ == "__main__":
    analyze_map_file_manually()
