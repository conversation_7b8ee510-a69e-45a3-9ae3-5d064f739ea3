#!/usr/bin/env python3

import sys
import os
sys.path.append('src')

from map_handle import MapFileParser

def test_improved_classification():
    """测试改进后的段分类和模块统计"""
    
    map_file = 'int_rpu_arm8r5_arm_gcc.elf.map'
    
    print("测试改进后的段分类和模块统计")
    print("=" * 80)
    
    # 创建解析器并分析文件
    parser = MapFileParser(map_file)
    parser.parse_map_file()
    
    # 获取两种统计结果
    text1, data1, bss1, other1 = parser.get_memory_usage()
    text2, data2, bss2, other2 = parser.get_module_memory_usage()
    
    print("=== 修改前后对比 ===")
    print("用户报告的原始数据:")
    print("  内存占用信息: text=2,181,408, data=1,086,819, bss=1,175,104, 总计=4,443,331")
    print("  模块占用分析: text=36,060, data=1,124,873, bss=1,665,322, other=2,097,360, 总计=4,923,615")
    print()
    
    print("=== 修改后的结果 ===")
    print("内存占用信息 (基于主段统计):")
    total1 = text1 + data1 + bss1 + other1
    print(f"  text:  {text1:>10,} bytes ({text1/total1*100:5.1f}%)")
    print(f"  data:  {data1:>10,} bytes ({data1/total1*100:5.1f}%)")
    print(f"  bss:   {bss1:>10,} bytes ({bss1/total1*100:5.1f}%)")
    if other1 > 0:
        print(f"  other: {other1:>10,} bytes ({other1/total1*100:5.1f}%)")
    print(f"  总计:  {total1:>10,} bytes")
    
    print()
    print("模块占用分析 (基于子段统计, 改进后):")
    total2 = text2 + data2 + bss2 + other2
    print(f"  text:  {text2:>10,} bytes ({text2/total2*100:5.1f}%)")
    print(f"  data:  {data2:>10,} bytes ({data2/total2*100:5.1f}%)")
    print(f"  bss:   {bss2:>10,} bytes ({bss2/total2*100:5.1f}%)")
    if other2 > 0:
        print(f"  other: {other2:>10,} bytes ({other2/total2*100:5.1f}%)")
    print(f"  总计:  {total2:>10,} bytes")
    
    print()
    print("=== 改进效果分析 ===")
    
    # 计算差异
    text_diff = abs(text1 - text2)
    data_diff = abs(data1 - data2)
    bss_diff = abs(bss1 - bss2)
    total_diff = abs(total1 - total2)
    
    print(f"text差异:  {text_diff:>10,} bytes ({text_diff/max(text1,text2)*100:5.1f}%)")
    print(f"data差异:  {data_diff:>10,} bytes ({data_diff/max(data1,data2)*100:5.1f}%)")
    print(f"bss差异:   {bss_diff:>10,} bytes ({bss_diff/max(bss1,bss2)*100:5.1f}%)")
    print(f"总计差异:  {total_diff:>10,} bytes ({total_diff/max(total1,total2)*100:5.1f}%)")
    
    print()
    print("=== 改进成果 ===")
    
    if other2 == 0:
        print("✓ 成功移除了模块占用分析中的other分类")
    else:
        print(f"⚠ 模块占用分析中仍有other: {other2:,} bytes")
    
    if text2 > 1000000:  # 如果text超过1MB，说明分类改进有效
        print("✓ 模块占用分析的text统计显著改善")
    else:
        print("⚠ 模块占用分析的text统计仍然偏低")
    
    if total_diff < total1 * 0.05:  # 差异小于5%
        print("✓ 两种统计方法的总计基本一致")
    else:
        print("⚠ 两种统计方法仍存在较大差异")
    
    # 测试段分类器
    print()
    print("=== 段分类器测试 ===")
    test_sections = [
        '.text',
        '.text.main',
        '.OS_INTVEC_CORE0_CODE',
        '.OS_INTVEC_CORE1_CODE',
        '.rodata',
        '.data',
        '.bss',
        '.HYP_STACK_C0',
        '.SYS_STACK_C1',
        '.mcal_text',
        '.mcu_shm_noncache',
        '.ipc_bss',
        '.debug_info',
        '.ARM.exidx'
    ]
    
    print(f"{'段名':<25} {'分类'}")
    print("-" * 35)
    for section in test_sections:
        classification = parser._classify_section_type(section)
        print(f"{section:<25} {classification}")

if __name__ == "__main__":
    test_improved_classification()
