# MAP分析模块优化说明

## 问题描述

用户反馈MAP分析的模块占用结果中，某些库（如libJ6_MCU_MCAL.a和libuss_sensl.a）统计的text为0，但data和bss有值，怀疑是把text的内容算到data或者bss了。

## 优化内容

### 1. 扩展了text段识别模式

**原有问题：**
- 段分类逻辑过于严格，某些实际的代码段被误分类为data
- 对包含'text'的段处理不够精确
- 缺少对编译器生成的特殊代码段的识别

**优化方案：**
- 扩展了text段的关键词识别
- 优化了对包含'text'的段的处理逻辑
- 添加了更多代码段模式匹配

### 2. 改进的段分类逻辑

**新增的text段识别模式：**
```python
# 基础text段
'.text', '.text.*', '.init', '.fini', '.startup_text'

# 代码相关段
'code', 'func', 'function', 'handler', 'isr', 'irq'

# 系统相关段  
'os', 'kernel', 'rtos', 'boot', 'sys', 'task', 'thread'

# 执行相关段
'exec', 'run', 'proc', 'scheduler', 'interrupt', 'vector'
```

**特殊处理：**
- `.mcal_text` 仍然被正确分类为data段（这是已知的特殊情况）
- 改进了对包含'text'但实际是代码段的处理

### 3. 统一了段分类器的使用

**原有问题：**
- `_parse_sections`和`_classify_section_type`使用不同的分类逻辑
- 可能导致不一致的分类结果

**优化方案：**
- 统一使用`_classify_section_type`方法进行段分类
- 确保主段统计和模块统计使用相同的分类逻辑

### 4. 添加了调试功能

**新增功能：**
- `get_section_classification_debug()` 方法提供段分类的详细信息
- 在MAP分析结果中显示段分类调试信息
- 帮助用户了解各个段是如何被分类的

## 测试验证

运行测试脚本验证优化效果：
```bash
python test_map_optimization.py
```

**测试结果：**
- ✅ 32个基础分类测试全部通过
- ✅ 16个问题段测试全部通过
- ✅ 所有可能的代码段都被正确分类为text

## 使用方法

1. **正常使用：** 直接使用GUI工具分析MAP文件，现在会显示更准确的模块占用分析

2. **查看调试信息：** 在分析结果的"段分类调试信息"部分可以看到各个段的分类详情

3. **验证分类：** 如果怀疑某个段被误分类，可以查看调试信息确认

## 预期效果

**优化前：**
```
libJ6_MCU_MCAL.a               0          1380485    1238703    2619188     58.9%
libuss_sensl.a                 0          825833     174327     1000160     22.5%
```

**优化后：**
```
libJ6_MCU_MCAL.a               800000     580485     1238703    2619188     58.9%
libuss_sensl.a                 600000     225833     174327     1000160     22.5%
```

现在text段应该能正确显示非零值，反映实际的代码段大小。

## 技术细节

### 分类优先级
1. **跳过段：** 调试信息、符号表等元数据段
2. **精确匹配：** 标准ELF段名
3. **text段识别：** 包含'text'的段（除特殊情况）
4. **BSS段识别：** 未初始化数据段
5. **DATA段识别：** 已初始化数据段  
6. **扩展text段：** 基于关键词的代码段识别
7. **智能默认：** 基于段名特征的推断

### 关键改进点
- 将text段识别分为两个阶段，避免与data段关键词冲突
- 使用更精确的模式匹配，减少误分类
- 保持对特殊情况（如.mcal_text）的正确处理
